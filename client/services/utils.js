import get from 'lodash/get'
import vueLocalStorage from '@/services/local-storage'
import cloneDeep from 'lodash/cloneDeep'
import parser from 'cron-parser'
import {
  isEmpty,
  isEqual,
  keyBy,
  isNumber,
  isString,
  intersection,
  groupBy as lodashGroupBy,
  debounce as debounceCreator,
  uniqueId,
  pick,
  startCase,
  merge,
  omit
} from 'lodash'
import * as Excel from 'exceljs/dist/exceljs.min.js'
import ConfigApi from '@/services/config/api'
import constantStrings from '@/services/constants/constant-strings'
import { CHANNELS } from '@/services/constants'
import * as CSV from 'papaparse'
import { Parser as JsonToCSV} from 'json2csv'
import { isSalesUser, isPassportSuperAdmin, isPassportOrgAdmin } from './passportUtil'
import store from '@/store'

export function checkAnalyticsLite () {
  return store.state.bots?.featureflags?.IS_ANALYTICS_LITE?.value === 'true'
}

export function isAgentRecommendation () {
  return store.state.bots?.featureflags?.CHAT_PROFILER?.value === 'AGENT_RECOMMENDATION'
}
export function getNextScheduleFromCron (cronTxt, tz) {
  return parser.parseExpression(cronTxt, {tz, currentDate: new Date()}).next()
}

export function generateS3ObjectKeyPrefix (key) {
  const folderPrefix = store.state.bots?.currentBot?.id || 'ASSETS_WITHOUT_BOT_ID'
  return key ? `${folderPrefix}/${key}` : folderPrefix
}

export function alphaSort (sortPath) {
  return function sorter (a, b) {
    if (get(a, sortPath) && !get(b, sortPath)) {
      return -1
    } else if (get(b, sortPath) && !get(a, sortPath)) {
      return 1
    } else if (get(a, sortPath) && get(b, sortPath)) {
      const aComparator = get(a, sortPath).toString()
      const bComparator = get(b, sortPath).toString()

      if (aComparator < bComparator) {
        return -1
      } else if (bComparator < aComparator) {
        return 1
      } else {
        return 0
      }
    } else {
      return 0
    }
  }
}
export function genericArraySort (arr, key, order = 'asc', type = 'Number', secondOrderSortKey = null) {
  if (order === 'asc') {
    return arr.sort((a, b) => {
      if (type === 'String') {
        return String(a[key]).localeCompare(String(b[key]))
      } else {
        if (!secondOrderSortKey) return a[key] - b[key]
        else return a[key] - b[key] || String(a[secondOrderSortKey]).localeCompare(String(b[secondOrderSortKey]))
      }
    })
  } else if (order === 'desc') {
    return arr.sort((a, b) => {
      if (type === 'String') {
        return String(b[key]).localeCompare(String(a[key]))
      } else {
        if (!secondOrderSortKey) return b[key] - a[key]
        else return b[key] - a[key] || String(a[secondOrderSortKey]).localeCompare(String(b[secondOrderSortKey]))
      }
    })
  } else {
    return arr
  }
}

export function toTitleCase (str) {
  return str?.replace(/\w\S*/g, txt => {
    return txt.charAt(0).toUpperCase() + txt.substr(1).toLowerCase()
  })
}

export function toTitleCaseWithPreservedSymbols (str) {
  // Handle case with preserved symbols (hyphens, underscores)
  return str.replace(/[^a-zA-Z0-9]+([a-zA-Z])/g, (match, char) => {
    return match.slice(0, -1) + char.toUpperCase()
  }).replace(/^([a-zA-Z])/, (match, char) => {
    return char.toUpperCase()
  })
}

export function camelToTitleCase (str) {
  return startCase(str)
}

export function truncateMiddle (text, maxLength) {
  if (text.length <= maxLength) {
    return text
  }

  const halfLength = Math.floor(maxLength / 2)
  const omission = '...'
  const omissionLength = omission.length

  const startText = text.slice(0, halfLength)
  const endText = text.slice(-halfLength + omissionLength)

  return startText + omission + endText
}

export function alphaSortCaseInsensitive (sortPath) {
  return function sorter (a, b) {
    if (get(a, sortPath) && !get(b, sortPath)) {
      return -1
    } else if (get(b, sortPath) && !get(a, sortPath)) {
      return 1
    } else if (get(a, sortPath) && get(b, sortPath)) {
      const aComparator = get(a, sortPath).toString().toLowerCase()
      const bComparator = get(b, sortPath).toString().toLowerCase()

      if (aComparator < bComparator) {
        return -1
      } else if (bComparator < aComparator) {
        return 1
      } else {
        return 0
      }
    } else {
      return 0
    }
  }
}

export const dateTimeFormat = {
  longDate: 'MMM DD YYYY',
  longDateComma: 'MMM DD, YYYY',
  shortDate: 'MMM DD',
  longDateWithDay: 'MMM DD YYYY, dddd',
  day: 'MMM DD YYYY',
  week: 'MMM DD YYYY',
  month: 'MMM YYYY',
  year: 'YYYY',
  dayMonth: 'Do MMM',
  dateMonth: 'D MMM YYYY',
  timestamp: 'MMMM Do YYYY, HH:mm:ss',
  slashDay: 'DD/MM/YYYY',
  dotDay: 'MM.DD.YYYY',
  hours24WithoutColon: 'HH00',
  auditFormat: 'h:mm A on DD/MM/YYYY',
  buildFormat: 'h:mm A on DD/MM/YYYY',
  monthNameDayYearWithTime: 'MMMM DD, YYYY h:mm A',
  dateWithHyphen: 'DD-MM-YYYY HH:mm',
  yearMonthDayWithTime: 'YYYY-MM-DD HH:mm:ss',
  monthDayYearWithTime: 'MMM DD, YYYY, hh:mm A',
  minuteSecWithMeridiem: 'HH:mm',
  timezoneWithAbbreviation: 'Z (z)',
  slashDayTime: 'DD/MM/YYYY HH:mm A',
  slashDayTimeWithComma: 'MM/DD/YYYY, HH:mm A',
  hourStartFormat: 'YYYY-MM-DD HH:00:00',
  hourEndFormat: 'YYYY-MM-DD HH:59:59'
}
export function delay (time, thisArg) {
  return new Promise((resolve) => {
    setTimeout(resolve.bind(null, thisArg), time)
  })
}

export function deepClone (value) {
  return cloneDeep(value)
}

export function isANumber (value) {
  return isNumber(value)
}

export function isAString (value) {
  return isString(value)
}

export function isAnArray (value) {
  return Array.isArray(value)
}

export function getVal (source, valKey) {
  return get(source, valKey)
}

export function capitalizeFirstLetter (string) {
  return string.charAt(0).toUpperCase() + string.slice(1).toLowerCase()
}

export function randomDigits (length) {
  const digitLength = parseInt(1 + new Array(length + 1).join('0'))
  return Math.floor(Math.random() * digitLength)
}

export function isValuesEqual (value, compareValue) {
  return isEqual(value, compareValue)
}

export function keyByObject (key, array) {
  return keyBy(key, array)
}

export function debounce (func, interval) {
  return debounceCreator(func, interval)
}

export function uniqueIdGenerator (prefix) {
  return uniqueId(`${prefix}_`)
}

export function isValueEmpty (value) {
  return isEmpty(value)
}
export function omitValues (...args) {
  return omit(...args)
}

export function findProperty (data, prop, defaultVal) {
  const found = data?.find(item => item.name === prop)
  return (found && found.value) || defaultVal
}

export function arrayIntersection (...arrays) {
  return intersection(...arrays)
}

export function groupBy (collection, key) {
  return lodashGroupBy(collection, key)
}

export function objectPick (data, array) {
  return pick(data, array)
}

export function mergeObject (...objects) {
  return merge({}, ...objects)
}
export function hasAllKeys (obj, source) {
  for (const key in source) {
    if (!(key in obj)) {
      return false
    }
    if (typeof source[key] === 'object' && source[key] !== null) {
      if (typeof obj[key] !== 'object' || obj[key] === null) {
        return false
      }
      if (!hasAllKeys(obj[key], source[key])) {
        return false
      }
    }
  }
  return true
}

export function DOMParser (html) {
  return (new window.DOMParser()).parseFromString(html, 'text/html').documentElement.textContent
}

export async function readExcel (fileBuffer) {
  const workbook = new Excel.Workbook()
  let response
  await workbook.xlsx
    .load(fileBuffer)
    .then((wb) => {
      wb.eachSheet(sheet => {
        response = sheet.getSheetValues()
      })
    })
    .catch(error => { response = error })
  return response
}

export function readCSV (file) {
  return new Promise((resolve, reject) => {
    CSV.parse(file, {
      header: true,
      complete: (result) => {
        resolve(result.data)
      },
      error: (error) => {
        reject(error)
      }
    })
  })
}

export function excelFileValuesReader (fileObj, validatorFunc) {
  const fileExt = fileObj.raw.name && fileObj.raw.name.split('.').pop()
  if (fileExt !== 'xlsx') return false
  const reader = new window.FileReader()
  return new Promise((resolve, reject) => {
    reader.onerror = () => {
      reader.abort()
      console.error('Problem parsing input file.')
    }
    reader.onload = async () => {
      try {
        const values = await readExcel(reader.result)
        const isValid = validatorFunc(values)
        return resolve(isValid)
      } catch (error) {
        return reject(error)
      }
    }
    reader.readAsArrayBuffer(fileObj.raw)
  })
}

export function CSVFileReader (fileObj, validatorFunc) {
  const fileExt = fileObj.raw.name && fileObj.raw.name.split('.').pop()
  if (fileExt !== 'csv') return false
  const reader = new window.FileReader()
  return new Promise(resolve => {
    reader.onerror = () => {
      reader.abort()
      console.error('Problem parsing input file.')
    }
    reader.onload = async () => {
      try {
        const isValid = validatorFunc(reader.result)
        return resolve(isValid)
      } catch (error) {
        return resolve(false)
      }
    }
    reader.readAsText(fileObj.raw)
  })
}

export function findFirst (findIn, key) {
  let found
  if (findIn[key]) {
    found = findIn[key]
    return found
  }

  findIn = Object.values(findIn)
  if (findIn.length) {
    for (let i = 0; i < findIn.length; i++) {
      if (findIn[i][key]) {
        found = findIn[i][key]
        break
      }
      if (typeof findIn[i] === 'object') {
        const findInCopy = { ...findIn[i] }
        findFirst(findInCopy, key)
      }
    }
  }
  return found
}

export async function getConfigValueByKey (configKey) {
  try {
    return await ConfigApi.getConfig(configKey)
  } catch (e) {
    console.log('Error in getting ' + configKey + ' form config')
  }
}

export function loadScript (attributes) {
  let script = attributes?.id && document.getElementById(attributes.id)
  if (script) return
  script = document.createElement('script')
  for (const key in attributes) {
    script[key] = attributes[key]
  }
  document.head.appendChild(script)
}

export function getMappedChannel (channel) {
  const channels = CHANNELS.MULTI_CHANNELS
  if (channel.uuid === constantStrings.en.DEFAULT_MULTI_CHANNEL_ID) {
    return { channelValue: constantStrings.en.NETOMI_WEB_WIDGET, channelLabel: constantStrings.en.Netomi_Web_Widget }
  } else if (channels[channel.channelId.value]) {
    return { channelValue: channel?.channelId?.value, channelLabel: channel?.channelId?.label }
  } else if (channel?.channelType?.value === constantStrings.en.CHAT) {
    return { channelValue: constantStrings.en.CHAT_API, channelLabel: constantStrings.en.CHAT_API }
  } else {
    return { channelValue: constantStrings.en.EMAIL_API, channelLabel: constantStrings.en.EMAIL_API }
  }
}

export function isObject (item) {
  return (item && typeof item === 'object' && !Array.isArray(item))
}

export function deepObjectMerge (target, source) {
  for (const key in source) {
    if (isObject(source[key])) {
      if (!(key in target)) target[key] = {}
      deepObjectMerge(target[key], source[key])
    } else {
      target[key] = source[key]
    }
  }
}

export function getShade (color, percent) {
  if (color.length === 4) { // if hex code is three char hex code. Convert it to 6 char hex code
    color = '#' + color[1].repeat(2) + color[2].repeat(2) + color[3].repeat(2)
  }

  let R = parseInt(color.substring(1, 3), 16)
  let G = parseInt(color.substring(3, 5), 16)
  let B = parseInt(color.substring(5, 7), 16)

  R = parseInt((R * (100 + percent)) / 100, 10)
  G = parseInt((G * (100 + percent)) / 100, 10)
  B = parseInt((B * (100 + percent)) / 100, 10)

  R = R < 255 ? R : 255
  G = G < 255 ? G : 255
  B = B < 255 ? B : 255

  const RR = R.toString(16).length === 1 ? '0' + R.toString(16) : R.toString(16)
  const GG = G.toString(16).length === 1 ? '0' + G.toString(16) : G.toString(16)
  const BB = B.toString(16).length === 1 ? '0' + B.toString(16) : B.toString(16)

  return '#' + RR + GG + BB
}

export function downloadJsonFileManually (jsonObj, fileName) {
  const dataStr = 'data:text/json;charset=utf-8,' + encodeURIComponent(jsonObj)
  const downloadAnchorNode = document.createElement('a')
  downloadAnchorNode.setAttribute('href', dataStr)
  downloadAnchorNode.setAttribute('download', fileName + '.json')
  document.body.appendChild(downloadAnchorNode) // required for firefox
  downloadAnchorNode.click()
  downloadAnchorNode.remove()
}

export function fractionSort (desc, param1, param2) {
  if (param2) {
    return (data1, data2) => {
      const fraction1 = data1[param2] ? data1[param1] / data1[param2] : 0
      const fraction2 = data2[param2] ? data2[param1] / data2[param2] : 0
      return (fraction1 - fraction2) * (desc ? -1 : 1)
    }
  } else {
    return (data1, data2) => {
      return (data1[param1] - data2[param1]) * (desc ? -1 : 1)
    }
  }
}

export function getUsersToBeFetched (userList, key = 'updatedBy') {
  const usersToBeFetched = {}
  userList.forEach(user => {
    if (user?.[key]) {
      if (typeof user[key] === 'object') user[key] = user[key]?.id
      usersToBeFetched[user[key]] = user[key]
    }
  })
  return Object.keys(usersToBeFetched).length ? Object.keys(usersToBeFetched) : []
}

export async function resolveUserDetails (listData, key = 'updatedBy') {
  const usersList = getUsersToBeFetched(listData, key)
  if (Object.keys(usersList).length) {
    const response = await store.dispatch(
      'fetchAndSetUserList',
      usersList,
      { root: true }
    )
    listData.forEach((data) => {
      data[key] = response[data[key]]
      return data
    })
  }
  return listData
}

export function formatTime (seconds) {
  const hrs = Math.floor(seconds / 3600)
  const mins = Math.floor((seconds % 3600) / 60)
  const secs = seconds % 60

  let formattedTime = ''
  if (hrs > 0) {
    formattedTime += `${hrs}h `
  }

  if (mins > 0) {
    formattedTime += `${mins}min `
  }

  if (secs > 0) {
    formattedTime += `${secs}sec`
  }

  // Trim any trailing space
  return formattedTime.trim()
}

export function numberFormatter (num, digits = 2) {
  if (num < 10e3) {
    return num?.toLocaleString()
  }

  const lookup = [
    { value: 1, symbol: '' },
    { value: 1e3, symbol: 'K' },
    { value: 1e6, symbol: 'M' },
    { value: 1e9, symbol: 'B' },
    { value: 1e12, symbol: 'T' },
    { value: 1e15, symbol: 'Q' },
    { value: 1e18, symbol: 'Q' }
  ]
  const rx = /\.0+$|(\.[0-9]*[1-9])0+$/
  const item = lookup.slice().reverse().find(item => num >= item.value)

  return item ? (num / item.value).toFixed(digits).replace(rx, '$1') + item.symbol : '0'
}

/**
 * This function returns a promise that uses the audio context and audio buffer data
 * to decode the audio data into consumable form.
 * DecodeAudioData is used to asynchronously decode audio file data contained in an ArrayBuffer.
 *
 * @param {AudioContext} context - the audio context that must have been initialized in views/components
 * @param {ArrayBuffer} audioData - This is the audio data in ArrayBuffer format that gets decoded.
 */
export function createAudioBuffer (context, audioData) {
  return new Promise((resolve, reject) => {
    context.decodeAudioData(
      audioData,
      (buffer) => {
        resolve(buffer)
      },
      (error) => {
        reject(error)
      }
    )
  })
}

/**
 * This function returns the short language locale from a complete locale
 * @example
 * en-US is a complete locale where English is the language and US is the region
 * Output for this would be `en` which is the language
 * @param {String} locale - The locale of a language
 * @returns - Language from the locale
 */
export function getLanguageFromLocale (locale) {
  if (window?.Intl && window.Intl?.Locale) {
    const intlLocale = new Intl.Locale(locale)
    return intlLocale.language
  }

  return locale.split('-')[0]
}

export function addHttpsProtocol (url) {
  if (!/^https?:\/\//i.test(url)) {
    return 'https://' + url
  }
  return url
}

export function downloadCSV (data, fileName, customHeaders) {
  const csvDataParser = new JsonToCSV({ fields: customHeaders })
  const csvData = csvDataParser.parse(data, {
    header: true,
    dynamicTyping: false
  })
  const blob = new Blob([csvData], { type: 'text/csv;charset=utf-8;' })
  const link = document.createElement('a')
  link.href = URL.createObjectURL(blob)
  link.download = fileName
  link.style.display = 'none'
  document.body.appendChild(link)
  link.click()
  document.body.removeChild(link)
}

export async function waitUntil (conditionFunc, interval, timeout) {
  return new Promise((resolve, reject) => {
    let elapsedTime = 0
    const intervalId = setInterval(async () => {
      elapsedTime += interval
      try {
        if (await conditionFunc() || elapsedTime >= timeout) {
          clearInterval(intervalId)
          resolve()
        }
      } catch (error) {
        clearInterval(intervalId)
        reject(error)
      }
    }, interval)
  })
}

export { isPassportSuperAdmin, isSalesUser, isPassportOrgAdmin }
